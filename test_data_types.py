#!/usr/bin/env python3
"""
Test script to validate data type handling in the ETL pipeline.
This script tests the fixes for data type mismatches between the source table schema
and the pipeline's data handling.
"""

import sys
import os
import logging
from decimal import Decimal
from datetime import datetime
import json

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config import load_config
from database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
)
logger = logging.getLogger(__name__)

def test_decimal_handling():
    """Test decimal data type handling."""
    logger.info("Testing decimal data type handling...")
    
    test_cases = [
        Decimal('123.45'),
        Decimal('999999999999999999.99'),
        <PERSON><PERSON><PERSON>('0.01'),
        <PERSON><PERSON><PERSON>('0'),
        None
    ]
    
    for test_value in test_cases:
        try:
            # Test decimal conversion logic from insert_batch
            if test_value is not None:
                converted = Decimal(str(test_value))
                logger.info(f"✓ Decimal conversion successful: {test_value} -> {converted}")
            else:
                logger.info(f"✓ NULL decimal value handled correctly: {test_value}")
        except Exception as e:
            logger.error(f"✗ Decimal conversion failed for {test_value}: {e}")

def test_varchar_length_validation():
    """Test VARCHAR length constraint validation."""
    logger.info("Testing VARCHAR length validation...")
    
    test_cases = [
        ("Normal text", 255, True),
        ("A" * 255, 255, True),
        ("A" * 256, 255, False),  # Should be truncated
        ("", 255, True),
        (None, 255, True)
    ]
    
    for test_value, max_length, should_pass in test_cases:
        try:
            # Simulate the varchar validation logic from insert_batch
            if test_value is not None:
                value = str(test_value)
                if len(value) > max_length:
                    truncated = value[:max_length]
                    logger.info(f"✓ VARCHAR truncation: {len(value)} chars -> {len(truncated)} chars")
                else:
                    logger.info(f"✓ VARCHAR within limits: {len(value)} chars")
            else:
                logger.info(f"✓ NULL VARCHAR value handled correctly")
        except Exception as e:
            logger.error(f"✗ VARCHAR validation failed for {test_value}: {e}")

def test_binary_data_handling():
    """Test binary data type handling."""
    logger.info("Testing binary data handling...")
    
    test_cases = [
        b'\x00\x01\x02\x03\x04\x05\x06\x07',  # 8 bytes (binary(8))
        b'',  # Empty binary
        b'\xff' * 8,  # Max values
        None
    ]
    
    for test_value in test_cases:
        try:
            # Test binary data handling (should remain as bytes)
            if isinstance(test_value, bytes):
                logger.info(f"✓ Binary data preserved: {len(test_value)} bytes")
            elif test_value is None:
                logger.info(f"✓ NULL binary value handled correctly")
            else:
                logger.warning(f"? Unexpected binary data type: {type(test_value)}")
        except Exception as e:
            logger.error(f"✗ Binary data handling failed for {test_value}: {e}")

def test_integer_overflow():
    """Test integer overflow handling."""
    logger.info("Testing integer overflow handling...")
    
    test_cases = [
        (2147483647, "int", True),      # Max int
        (2147483648, "int", False),     # Overflow int
        (-2147483648, "int", True),     # Min int
        (-2147483649, "int", False),    # Underflow int
        (0, "int", True),
        (None, "int", True)
    ]
    
    for test_value, data_type, should_pass in test_cases:
        try:
            if test_value is not None:
                value = int(test_value)
                max_int = 2147483647
                min_int = -2147483648
                if value > max_int or value < min_int:
                    logger.info(f"✓ Integer overflow detected and handled: {test_value}")
                else:
                    logger.info(f"✓ Integer within range: {test_value}")
            else:
                logger.info(f"✓ NULL integer value handled correctly")
        except Exception as e:
            logger.error(f"✗ Integer handling failed for {test_value}: {e}")

def test_datetime_handling():
    """Test datetime handling."""
    logger.info("Testing datetime handling...")
    
    test_cases = [
        datetime.now(),
        datetime(2023, 1, 1, 12, 0, 0),
        None
    ]
    
    for test_value in test_cases:
        try:
            if hasattr(test_value, 'isoformat'):
                iso_value = test_value.isoformat()
                logger.info(f"✓ Datetime converted to ISO format: {test_value} -> {iso_value}")
            elif test_value is None:
                logger.info(f"✓ NULL datetime value handled correctly")
            else:
                logger.warning(f"? Unexpected datetime type: {type(test_value)}")
        except Exception as e:
            logger.error(f"✗ Datetime handling failed for {test_value}: {e}")

def test_database_connection():
    """Test database connection with new schema."""
    logger.info("Testing database connection and schema...")
    
    try:
        config = load_config()
        
        # Test destination database connection
        dest_db = DatabaseManager(config.database['destination'])
        
        with dest_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Test if new columns exist in destination table
            table_name = f"[{config.database['destination'].db_schema}].[{config.database_objects.main_processed_documents_table}]"
            
            query = f"""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{config.database_objects.main_processed_documents_table}'
            AND TABLE_SCHEMA = '{config.database['destination'].db_schema}'
            ORDER BY ORDINAL_POSITION
            """
            
            cursor.execute(query)
            columns = cursor.fetchall()
            
            logger.info("Destination table schema:")
            for col in columns:
                logger.info(f"  {col[0]}: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
            
            # Check for new columns
            new_columns = [
                'Source_ID_Nullable', 'Transaction_DTS', 'ACC_Domain', 
                'Source_Data_Type', 'Operation_Sequence', 'Host_Field_Name',
                'Host_Id', 'Source_Size', 'Row_Version_Number'
            ]
            
            existing_columns = [col[0] for col in columns]
            for new_col in new_columns:
                if new_col in existing_columns:
                    logger.info(f"✓ New column exists: {new_col}")
                else:
                    logger.warning(f"✗ New column missing: {new_col}")
                    
        logger.info("✓ Database connection and schema test completed")
        
    except Exception as e:
        logger.error(f"✗ Database connection test failed: {e}")

def main():
    """Run all data type tests."""
    logger.info("Starting data type validation tests...")
    
    try:
        test_decimal_handling()
        test_varchar_length_validation()
        test_binary_data_handling()
        test_integer_overflow()
        test_datetime_handling()
        test_database_connection()
        
        logger.info("All data type validation tests completed!")
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
