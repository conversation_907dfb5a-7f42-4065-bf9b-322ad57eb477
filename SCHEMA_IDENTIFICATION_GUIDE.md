# Schema Identification and Fix Guide

## Current Status

The ETL pipeline encountered errors because the SQL query referenced columns that don't exist in your actual source table:
- `TRANSACTION_DTS` 
- `OPERATION_SEQUENCE`

I've temporarily reverted the SQL query to use only the columns we know exist, so the pipeline should work again. Now we need to identify the actual source table schema and complete the data type fixes properly.

## Step 1: Identify Actual Source Schema

Run the schema identification script:

```bash
python identify_source_schema.py
```

This script will:
- Connect to your source database
- Query the actual column names and data types in `TBL_BlobData`
- Show you which expected columns exist vs. which are missing
- Generate corrected SQL query recommendations

## Step 2: Review the Output

The script will show you something like:

```
Column Name              Data Type       Nullable   Max Length   Precision  Scale
--------------------------------------------------------------------------------
Id                       decimal         NO         -            18         2
Data                     image           YES        2147483647   -          -
Type                     varchar         YES        255          -          -
HostId                   decimal         YES        -            18         2
[... other actual columns ...]

Column availability check:
✓ Id - EXISTS
✓ Data - EXISTS  
✓ Type - EXISTS
✓ HostId - EXISTS
✗ TRANSACTION_DTS - MISSING
✗ OPERATION_SEQUENCE - MISSING
[... etc ...]
```

## Step 3: Update the SQL Query

Based on the actual schema, update `src/database.py` in the `fetch_batch` method.

### Example: If only Id, Data, Type, HostId exist

Replace the SELECT section with:

```sql
SELECT TOP (?)
    -- Confirmed existing columns
    CAST(BD.Id AS DECIMAL(18,2)) AS Id,
    BD.Data,
    BD.Type,
    CAST(BD.HostId AS DECIMAL(18,2)) AS HostId,
    
    -- Add any other columns that actually exist in your table
    -- (based on the schema identification output)
```

### Example: If additional columns exist with different names

If the script shows columns like `TransactionDate` instead of `TRANSACTION_DTS`, update accordingly:

```sql
SELECT TOP (?)
    CAST(BD.Id AS DECIMAL(18,2)) AS Id,
    BD.Data,
    BD.Type,
    CAST(BD.HostId AS DECIMAL(18,2)) AS HostId,
    BD.TransactionDate AS Transaction_DTS,  -- Use actual column name
    -- etc.
```

## Step 4: Update Pipeline Processing

Update `src/pipeline.py` in the `_process_record` method to handle the actual available columns:

```python
# Build destination record with available columns
dest_record = {
    # Always available
    'SourceId': record['Id'],
    'Host_Id': record.get('HostId'),
    'Source_Data_Type': record.get('Type'),
    
    # Add based on what actually exists in your source table
    'Transaction_DTS': record.get('TransactionDate'),  # If this column exists
    'ACC_Domain': record.get('AccountDomain'),         # If this column exists
    # Set to None for columns that don't exist in source
    'Operation_Sequence': None,                        # If this doesn't exist
    'Source_Size': None,                               # If this doesn't exist
    # etc.
}
```

## Step 5: Update Destination Table Schema

You may want to remove columns from the destination table that correspond to non-existent source columns, or keep them as NULL for future use.

### Option A: Remove unused columns
Create a new migration script to drop columns that won't be populated:

```sql
-- Drop columns for non-existent source fields
ALTER TABLE [dbo].[NicheBlobETLStaging] DROP COLUMN Operation_Sequence;
ALTER TABLE [dbo].[NicheBlobETLStaging] DROP COLUMN Source_Size;
-- etc.
```

### Option B: Keep columns for future use
Leave the columns in place - they'll just remain NULL until the source table is updated.

## Step 6: Test the Updated Pipeline

1. **Test the schema identification**:
   ```bash
   python identify_source_schema.py
   ```

2. **Test the data type handling**:
   ```bash
   python test_data_types.py
   ```

3. **Run a small batch**:
   ```bash
   python main.py  # with max_records_to_process: 5 in config
   ```

## Common Column Name Variations

Based on the error, your actual table might have columns with different names:

| Expected Name | Possible Actual Names |
|---------------|----------------------|
| TRANSACTION_DTS | TransactionDate, Transaction_Date, TxnDate, CreatedDate |
| OPERATION_SEQUENCE | OperationSeq, OpSequence, Sequence, SeqNum |
| ACCDomain | AccountDomain, Account_Domain, Domain |
| HostFieldName | HostField, Host_Field, FieldName |
| RowVersionNumber | RowVersion, Timestamp, Version |

## Quick Fix Template

Here's a template for the most common scenario where only basic columns exist:

### Update src/database.py (lines ~187-199):
```python
query = f"""
WITH cte AS (
    SELECT TOP (?)
        CAST(BD.Id AS DECIMAL(18,2)) AS Id,
        BD.Data,
        BD.Type,
        CAST(BD.HostId AS DECIMAL(18,2)) AS HostId,
        -- Add other actual columns here based on schema identification
```

### Update src/pipeline.py (lines ~317-331):
```python
dest_record = {
    'SourceId': record['Id'],
    'Host_Id': record.get('HostId'),
    'Source_Data_Type': record.get('Type'),
    # Set non-existent columns to None
    'Source_ID_Nullable': None,
    'Transaction_DTS': None,
    'ACC_Domain': None,
    'Operation_Sequence': None,
    'Host_Field_Name': None,
    'Source_Size': None,
    'Row_Version_Number': None,
```

## Next Steps After Schema Identification

1. Run `python identify_source_schema.py` to see your actual schema
2. Update the SQL query with actual column names
3. Update the pipeline processing logic
4. Test with a small batch
5. Update this guide with your specific findings for future reference

## Need Help?

If you encounter issues:
1. Share the output from `identify_source_schema.py`
2. I can provide specific SQL and Python code updates based on your actual schema
3. We can create a custom migration plan for your specific table structure
