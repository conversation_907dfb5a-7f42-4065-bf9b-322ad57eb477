-- Create destination tables for Niche Text ETL on the destination SQL Server database
-- Note: This script uses 4-part naming convention for consistency

-- Create the main processed documents table


IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{main_processed_documents_table}')
BEGIN
    CREATE TABLE [{destination_schema}].[{main_processed_documents_table}] (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,

        -- Source table columns with proper data types
        SourceId DECIMAL(18,2) NOT NULL,              -- Changed from BIGINT to match source Id (decimal)
        Source_ID_Nullable DECIMAL(18,2) NULL,        -- Source ID column (nullable decimal)
        Transaction_DTS DATETIME2 NULL,               -- Source TRANSACTION_DTS
        ACC_Domain NVARCHAR(255) NULL,                -- Source ACCDomain (varchar(255))
        Source_Data_Type NVARCHAR(255) NULL,          -- Source Type (varchar(255)) - renamed to avoid confusion
        Operation_Sequence INT NULL,                  -- Source OPERATION_SEQUENCE
        Host_Field_Name NVARCHAR(255) NULL,           -- Source HostFieldName (varchar(255))
        Host_Id DECIMAL(18,2) NULL,                   -- Source HostId (decimal)
        Source_Size INT NULL,                         -- Source Size
        Row_Version_Number BINARY(8) NULL,            -- Source RowVersionNumber (binary(8))

        -- Derived and processed columns
        Niche_Report_ID BIGINT,
        Entered_Time DATETIME2,
        Report_Time DATETIME2,
        Remarks NVARCHAR(MAX),
        Niche_Author_ID BIGINT,
        Niche_Enter_ID BIGINT,
        Niche_Occurrence_ID BIGINT,
        Occurrence_Number NVARCHAR(50),
        Occurrence_Type NVARCHAR(100),
        Zone NVARCHAR(50),
        Team NVARCHAR(50),
        Municipality NVARCHAR(100),
        real_type NVARCHAR(50),
        category NVARCHAR(50),
        normalized_text NVARCHAR(MAX),
        text_length INT,
        original_size BIGINT,
        decompressed_size BIGINT,
        compression_ratio FLOAT,
        parser_used NVARCHAR(100),
        ProcessedTime DATETIME2 DEFAULT GETDATE(),
        parsing_successful BIT DEFAULT 1,

        -- Text embedding columns (for future smart search)
        text_embedding NVARCHAR(MAX),  -- JSON array of embedding vector
        embedding_dimension INT,       -- Dimension of the embedding vector
        chunk_count INT,              -- Number of chunks for long documents
        embedding_type NVARCHAR(50),  -- 'single' or 'chunked'
        
        -- Indexes
        INDEX IX_NicheBlobETLStaging_SourceId (SourceId),
        INDEX IX_NicheBlobETLStaging_Niche_Report_ID (Niche_Report_ID),
        INDEX IX_NicheBlobETLStaging_Occurrence_ID (Niche_Occurrence_ID),
        INDEX IX_NicheBlobETLStaging_Category (category),
        INDEX IX_NicheBlobETLStaging_ProcessedTime (ProcessedTime),
        INDEX IX_NicheBlobETLStaging_RealType (real_type),
        INDEX IX_NicheBlobETLStaging_EmbeddingType (embedding_type)
    );
    
    PRINT 'Created table: {main_processed_documents_table}';
END
ELSE
BEGIN
    PRINT 'Table {main_processed_documents_table} already exists';
END

-- Create the ETL checkpoints table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{etl_checkpoints_table}')
BEGIN
    CREATE TABLE [{destination_schema}].[{etl_checkpoints_table}] (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        ProcessName NVARCHAR(100) NOT NULL UNIQUE,
        LastProcessedID DECIMAL(18,2) NOT NULL DEFAULT 0,  -- Changed from BIGINT to match source Id type
        LastUpdated DATETIME2 DEFAULT GETDATE(),
        
        -- Index
        INDEX IX_NicheBlobETLCheckpoints_ProcessName (ProcessName)
    );
    
    PRINT 'Created table: {etl_checkpoints_table}';
END
ELSE
BEGIN
    PRINT 'Table {etl_checkpoints_table} already exists';
END

-- Create the processing statistics table (optional)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{processing_statistics_table}')
BEGIN
    CREATE TABLE [{destination_schema}].[{processing_statistics_table}] (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        RunDate DATE NOT NULL,
        StartTime DATETIME2 NOT NULL,
        EndTime DATETIME2,
        RecordsProcessed INT DEFAULT 0,
        RecordsSuccessful INT DEFAULT 0,
        RecordsFailed INT DEFAULT 0,
        BytesProcessed BIGINT DEFAULT 0,
        TextExtracted BIGINT DEFAULT 0,
        DurationSeconds INT,
        SuccessRate FLOAT,
        ErrorSummary NVARCHAR(MAX),
        
        -- Index
        INDEX IX_NicheBlobETLStatistics_RunDate (RunDate),
        INDEX IX_NicheBlobETLStatistics_StartTime (StartTime)
    );
    
    PRINT 'Created table: {processing_statistics_table}';
END
ELSE
BEGIN
    PRINT 'Table {processing_statistics_table} already exists';
END

-- Create the document chunk embeddings table (for future smart search)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = '{document_chunk_embeddings_table}')
BEGIN
    CREATE TABLE [{destination_schema}].[{document_chunk_embeddings_table}] (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        DocumentId BIGINT NOT NULL,  -- References NicheBlobETLStaging.Id
        SourceId BIGINT NOT NULL,    -- References original source record
        ChunkIndex INT NOT NULL,     -- Index of chunk within document
        ChunkText NVARCHAR(MAX),     -- Text content of the chunk
        ChunkEmbedding NVARCHAR(MAX), -- JSON array of embedding vector
        ChunkLength INT,             -- Length of chunk text
        EmbeddingDimension INT,      -- Dimension of embedding vector
        CreatedTime DATETIME2 DEFAULT GETDATE(),

        -- Indexes
        INDEX IX_NicheBlobETLEmbeddings_DocumentId (DocumentId),
        INDEX IX_NicheBlobETLEmbeddings_SourceId (SourceId),
        INDEX IX_NicheBlobETLEmbeddings_ChunkIndex (ChunkIndex),

        -- Foreign key constraint (optional, uncomment when ready)
        -- FOREIGN KEY (DocumentId) REFERENCES {main_processed_documents_table}(Id)
    );

    PRINT 'Created table: {document_chunk_embeddings_table}';
END
ELSE
BEGIN
    PRINT 'Table {document_chunk_embeddings_table} already exists';
END

-- Create a view for easy querying of processed documents
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = '{processed_documents_view}')
BEGIN
    EXEC('
    CREATE VIEW [{destination_schema}].[{processed_documents_view}] AS
    SELECT
        pd.Id,
        pd.SourceId,
        pd.Niche_Report_ID,
        pd.Entered_Time,
        pd.Report_Time,
        pd.Occurrence_Number,
        pd.Occurrence_Type,
        pd.Zone,
        pd.Team,
        pd.Municipality,
        pd.real_type,
        pd.category,
        pd.text_length,
        pd.original_size,
        pd.decompressed_size,
        pd.compression_ratio,
        pd.parser_used,
        pd.ProcessedTime,
        pd.parsing_successful,
        -- Text preview (first 500 characters)
        LEFT(pd.normalized_text, 500) +
            CASE WHEN LEN(pd.normalized_text) > 500 THEN ''...'' ELSE '''' END AS text_preview
    FROM [{destination_schema}].[{main_processed_documents_table}] pd
    ');
    
    PRINT 'Created view: {processed_documents_view}';
END
ELSE
BEGIN
    PRINT 'View {processed_documents_view} already exists';
END

-- Insert initial checkpoint record
IF NOT EXISTS (SELECT * FROM [{destination_schema}].[{etl_checkpoints_table}] WHERE ProcessName = '{initial_checkpoint_process_name}')
BEGIN
    INSERT INTO [{destination_schema}].[{etl_checkpoints_table}] (ProcessName, LastProcessedID, LastUpdated)
    VALUES ('{initial_checkpoint_process_name}', 0, GETDATE());
    
    PRINT 'Inserted initial checkpoint record';
END
ELSE
BEGIN
    PRINT 'Checkpoint record already exists';
END

-- Create stored procedure for updating statistics
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = '{update_statistics_procedure}')
BEGIN
    EXEC('
    CREATE PROCEDURE [{destination_schema}].[{update_statistics_procedure}]
        @RunDate DATE,
        @StartTime DATETIME2,
        @EndTime DATETIME2 = NULL,
        @RecordsProcessed INT = 0,
        @RecordsSuccessful INT = 0,
        @RecordsFailed INT = 0,
        @BytesProcessed BIGINT = 0,
        @TextExtracted BIGINT = 0,
        @ErrorSummary NVARCHAR(MAX) = NULL
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @DurationSeconds INT = NULL;
        DECLARE @SuccessRate FLOAT = NULL;
        
        IF @EndTime IS NOT NULL
        BEGIN
            SET @DurationSeconds = DATEDIFF(SECOND, @StartTime, @EndTime);
            IF @RecordsProcessed > 0
                SET @SuccessRate = CAST(@RecordsSuccessful AS FLOAT) / @RecordsProcessed * 100;
        END
        
        MERGE [{destination_schema}].[{processing_statistics_table}] AS target
        USING (SELECT @RunDate as RunDate, @StartTime as StartTime) AS source
        ON target.RunDate = source.RunDate AND target.StartTime = source.StartTime
        WHEN MATCHED THEN
            UPDATE SET
                EndTime = @EndTime,
                RecordsProcessed = @RecordsProcessed,
                RecordsSuccessful = @RecordsSuccessful,
                RecordsFailed = @RecordsFailed,
                BytesProcessed = @BytesProcessed,
                TextExtracted = @TextExtracted,
                DurationSeconds = @DurationSeconds,
                SuccessRate = @SuccessRate,
                ErrorSummary = @ErrorSummary
        WHEN NOT MATCHED THEN
            INSERT (RunDate, StartTime, EndTime, RecordsProcessed, RecordsSuccessful,
                   RecordsFailed, BytesProcessed, TextExtracted, DurationSeconds,
                   SuccessRate, ErrorSummary)
            VALUES (@RunDate, @StartTime, @EndTime, @RecordsProcessed, @RecordsSuccessful,
                   @RecordsFailed, @BytesProcessed, @TextExtracted, @DurationSeconds,
                   @SuccessRate, @ErrorSummary);
    END
    ');
    
    PRINT 'Created stored procedure: {update_statistics_procedure}';
END
ELSE
BEGIN
    PRINT 'Stored procedure {update_statistics_procedure} already exists';
END

PRINT 'Database setup completed successfully!';
PRINT '';
PRINT 'Next steps:';
PRINT '1. Update config.yaml with your database connection details and credentials';
PRINT '2. Test the configuration using: python main.py';
PRINT '3. Run the ETL pipeline to process documents';
