-- Migration script to update existing ETL tables for proper data type handling
-- This script adds new columns and updates data types to match the source table schema
-- Run this script on your destination database before running the updated ETL pipeline

PRINT 'Starting data type migration for ETL tables...';
PRINT '';

-- Variables for table names (replace with your actual values)
DECLARE @destination_schema NVARCHAR(50) = 'dbo';  -- Update this to your schema
DECLARE @main_table NVARCHAR(100) = 'NicheBlobETLStaging';  -- Update this to your table name
DECLARE @checkpoint_table NVARCHAR(100) = 'NicheBlobETLCheckpoints';  -- Update this to your table name

-- Step 1: Add new columns to main processed documents table
PRINT 'Step 1: Adding new source columns to main table...';

-- Check if columns already exist before adding them
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Source_ID_Nullable')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Source_ID_Nullable DECIMAL(18,2) NULL');
    PRINT '  Added column: Source_ID_Nullable (DECIMAL(18,2))';
END
ELSE
    PRINT '  Column Source_ID_Nullable already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Transaction_DTS')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Transaction_DTS DATETIME2 NULL');
    PRINT '  Added column: Transaction_DTS (DATETIME2)';
END
ELSE
    PRINT '  Column Transaction_DTS already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'ACC_Domain')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD ACC_Domain NVARCHAR(255) NULL');
    PRINT '  Added column: ACC_Domain (NVARCHAR(255))';
END
ELSE
    PRINT '  Column ACC_Domain already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Source_Data_Type')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Source_Data_Type NVARCHAR(255) NULL');
    PRINT '  Added column: Source_Data_Type (NVARCHAR(255))';
END
ELSE
    PRINT '  Column Source_Data_Type already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Operation_Sequence')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Operation_Sequence INT NULL');
    PRINT '  Added column: Operation_Sequence (INT)';
END
ELSE
    PRINT '  Column Operation_Sequence already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Host_Field_Name')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Host_Field_Name NVARCHAR(255) NULL');
    PRINT '  Added column: Host_Field_Name (NVARCHAR(255))';
END
ELSE
    PRINT '  Column Host_Field_Name already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Host_Id')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Host_Id DECIMAL(18,2) NULL');
    PRINT '  Added column: Host_Id (DECIMAL(18,2))';
END
ELSE
    PRINT '  Column Host_Id already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Source_Size')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Source_Size INT NULL');
    PRINT '  Added column: Source_Size (INT)';
END
ELSE
    PRINT '  Column Source_Size already exists';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'Row_Version_Number')
BEGIN
    EXEC('ALTER TABLE [' + @destination_schema + '].[' + @main_table + '] 
          ADD Row_Version_Number BINARY(8) NULL');
    PRINT '  Added column: Row_Version_Number (BINARY(8))';
END
ELSE
    PRINT '  Column Row_Version_Number already exists';

PRINT '';

-- Step 2: Update SourceId column data type (this is more complex and may require data migration)
PRINT 'Step 2: Checking SourceId column data type...';

DECLARE @current_data_type NVARCHAR(50);
SELECT @current_data_type = DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = @main_table AND COLUMN_NAME = 'SourceId';

IF @current_data_type = 'bigint'
BEGIN
    PRINT '  WARNING: SourceId is currently BIGINT but should be DECIMAL(18,2)';
    PRINT '  This change requires careful data migration. Consider:';
    PRINT '  1. Create a backup of your data';
    PRINT '  2. Add a new column SourceId_New DECIMAL(18,2)';
    PRINT '  3. Copy data: UPDATE table SET SourceId_New = CAST(SourceId AS DECIMAL(18,2))';
    PRINT '  4. Drop old column and rename new column';
    PRINT '  For now, keeping existing BIGINT type for compatibility.';
END
ELSE IF @current_data_type = 'decimal'
BEGIN
    PRINT '  SourceId is already DECIMAL type - OK';
END
ELSE
BEGIN
    PRINT '  SourceId has unexpected data type: ' + @current_data_type;
END

PRINT '';

-- Step 3: Update checkpoint table
PRINT 'Step 3: Updating checkpoint table data type...';

DECLARE @checkpoint_data_type NVARCHAR(50);
SELECT @checkpoint_data_type = DATA_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = @checkpoint_table AND COLUMN_NAME = 'LastProcessedID';

IF @checkpoint_data_type = 'bigint'
BEGIN
    PRINT '  WARNING: LastProcessedID is currently BIGINT but should be DECIMAL(18,2)';
    PRINT '  This change requires data migration. For now, keeping BIGINT for compatibility.';
    PRINT '  The pipeline will handle type conversion automatically.';
END
ELSE IF @checkpoint_data_type = 'decimal'
BEGIN
    PRINT '  LastProcessedID is already DECIMAL type - OK';
END
ELSE
BEGIN
    PRINT '  LastProcessedID has unexpected data type: ' + @checkpoint_data_type;
END

PRINT '';

-- Step 4: Create indexes for new columns (optional but recommended)
PRINT 'Step 4: Creating indexes for new columns...';

-- Index on Transaction_DTS for time-based queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_' + @main_table + '_Transaction_DTS')
BEGIN
    EXEC('CREATE INDEX IX_' + @main_table + '_Transaction_DTS ON [' + @destination_schema + '].[' + @main_table + '] (Transaction_DTS)');
    PRINT '  Created index on Transaction_DTS';
END
ELSE
    PRINT '  Index on Transaction_DTS already exists';

-- Index on Operation_Sequence for sequence-based queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_' + @main_table + '_Operation_Sequence')
BEGIN
    EXEC('CREATE INDEX IX_' + @main_table + '_Operation_Sequence ON [' + @destination_schema + '].[' + @main_table + '] (Operation_Sequence)');
    PRINT '  Created index on Operation_Sequence';
END
ELSE
    PRINT '  Index on Operation_Sequence already exists';

PRINT '';
PRINT 'Data type migration completed successfully!';
PRINT '';
PRINT 'Next steps:';
PRINT '1. Test the updated ETL pipeline with: python test_data_types.py';
PRINT '2. Run a small batch to verify data type handling';
PRINT '3. Monitor logs for any data type conversion warnings';
PRINT '4. Consider full data migration for SourceId and LastProcessedID if needed';
