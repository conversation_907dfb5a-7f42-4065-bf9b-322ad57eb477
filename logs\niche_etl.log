2025-06-18 14:51:48 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:51:48 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:51:48 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:51:48 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:51:48 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:51:48 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:51:48 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Text embedding module disabled by configuration
2025-06-18 14:51:48 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is disabled
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 2
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:51:48 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: DISABLED
2025-06-18 14:51:48 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:51:48 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:51:48 | WARNING | src.config_validator:_log_validation_summary:254 |   - Text embedding module disabled by configuration
2025-06-18 14:51:48 | INFO | src.pipeline:__init__:124 | Pipeline initialized
2025-06-18 14:51:48 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:51:48 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 14:51:49 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 14:51:49 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:51:49 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:51:49 | WARNING | __main__:main:188 |   - Text embedding module disabled by configuration
2025-06-18 14:51:49 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:51:49 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:51:49 | INFO | src.database:execute_script:431 | SQL script executed successfully.
2025-06-18 14:51:49 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 14:51:49 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 14:51:49 | INFO | src.pipeline:run:132 | Starting ETL pipeline
2025-06-18 14:51:49 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 14:51:49 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 14:51:49 | INFO | src.database:get_last_processed_id:126 | Last processed ID for niche_text_etl: 0
2025-06-18 14:51:51 | INFO | src.database:fetch_batch:343 | Fetched batch of 1000 records (IDs: 10023001000000046127415 - 10023001000000046336135)
2025-06-18 14:51:51 | INFO | src.pipeline:run:168 | Processing batch 1 (5 records, total: 5/5)
2025-06-18 14:51:51 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1098 characters extracted
2025-06-18 14:51:51 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 666 characters extracted
2025-06-18 14:51:51 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 991 characters extracted
2025-06-18 14:51:51 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1592 characters extracted
2025-06-18 14:51:51 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 785 characters extracted
2025-06-18 14:51:51 | ERROR | src.database:get_connection:92 | Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)') (server=HQPRCAD94BI03, user=PAAdmin, trusted=False)
2025-06-18 14:51:51 | ERROR | src.database:insert_batch:416 | Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:51:51 | INFO | src.pipeline:_log_progress:360 | Progress: 5 processed, 5 successful, 0 failed, 100.0% success rate, 0.0 records/sec
2025-06-18 14:51:51 | INFO | src.pipeline:run:194 | Reached debug limit of 5 records. Processing complete.
2025-06-18 14:51:51 | INFO | src.pipeline:run:199 | Pipeline completed: {'records_processed': 5, 'records_successful': 5, 'records_failed': 0, 'success_rate': 100.0, 'bytes_processed': 3378, 'text_extracted': 5132, 'duration_seconds': 2.578389, 'records_per_second': 1.9391953657884826, 'error_count': 0, 'recent_errors': []}
2025-06-18 14:51:51 | INFO | __main__:main:203 | Pipeline execution completed successfully
2025-06-18 14:51:51 | INFO | __main__:main:204 | Final statistics: {
  "records_processed": 5,
  "records_successful": 5,
  "records_failed": 0,
  "success_rate": 100.0,
  "bytes_processed": 3378,
  "text_extracted": 5132,
  "duration_seconds": 2.578389,
  "records_per_second": 1.9391953657884826,
  "error_count": 0,
  "recent_errors": []
}
2025-06-18 14:51:51 | INFO | __main__:run_monitoring_if_enabled:115 | Running daily monitoring tasks...
2025-06-18 14:51:51 | INFO | src.monitoring:run_daily_monitoring:461 | Starting daily monitoring tasks
2025-06-18 14:51:51 | INFO | src.monitoring:run_daily_monitoring:472 | Analyzing logs for the past 24 hours
2025-06-18 14:51:51 | INFO | src.monitoring:run_daily_monitoring:476 | Checking if alert should be sent
2025-06-18 14:51:51 | INFO | src.monitoring:send_alert:226 | Email alerts disabled - skipping alert
2025-06-18 14:51:51 | INFO | src.monitoring:run_daily_monitoring:480 | Rotating old log files
2025-06-18 14:51:51 | INFO | src.monitoring:rotate_logs:429 | Log rotation complete: 0 deleted, 1 kept, 0 bytes freed
2025-06-18 14:51:51 | INFO | src.monitoring:run_daily_monitoring:483 | Daily monitoring tasks completed successfully
2025-06-18 14:51:51 | INFO | __main__:run_monitoring_if_enabled:119 | Monitoring completed: {
  "timestamp": "2025-06-18 14:51:51.666745",
  "log_analysis": {
    "period_start": "2025-06-17 14:51:51.666745",
    "period_end": "2025-06-18 14:51:51.666745",
    "errors": [
      {
        "file": "logs\\niche_etl.log",
        "line": 42,
        "timestamp": "2025-06-18 14:51:51",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)') (server=HQPRCAD94BI03, user=PAAdmin, trusted=False)",
        "context": "src.database:get_connection:92"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 43,
        "timestamp": "2025-06-18 14:51:51",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:416"
      }
    ],
    "warnings": [
      {
        "file": "logs\\niche_etl.log",
        "line": 5,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 7,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warning: Text embedding module disabled by configuration",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 16,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 17,
        "timestamp": "2025-06-18 14:51:48",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 18,
        "timestamp": "2025-06-18 14:51:48",
        "message": "- Text embedding module disabled by configuration",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 23,
        "timestamp": "2025-06-18 14:51:49",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 24,
        "timestamp": "2025-06-18 14:51:49",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 25,
        "timestamp": "2025-06-18 14:51:49",
        "message": "- Text embedding module disabled by configuration",
        "context": "__main__:main:188"
      }
    ],
    "total_errors": 2,
    "total_warnings": 8,
    "files_analyzed": [
      "logs\\niche_etl.log"
    ],
    "summary": {
      "has_errors": true,
      "has_warnings": true,
      "error_categories": {
        "Database": 1,
        "Other": 1
      },
      "warning_categories": {
        "Database": 3,
        "Other": 5
      },
      "most_common_errors": [],
      "most_common_warnings": []
    }
  },
  "email_sent": true,
  "log_rotation": {
    "files_deleted": [],
    "files_kept": [
      "logs\\niche_etl.log"
    ],
    "total_deleted": 0,
    "total_kept": 1,
    "space_freed": 0
  }
}
2025-06-18 14:51:51 | INFO | __main__:main:215 | Niche Text ETL completed successfully
2025-06-18 14:54:59 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 14:54:59 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 14:54:59 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 14:54:59 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 14:54:59 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:54:59 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 14:54:59 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Text embedding module disabled by configuration
2025-06-18 14:54:59 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is disabled
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 2
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 14:54:59 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: DISABLED
2025-06-18 14:54:59 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 14:54:59 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:54:59 | WARNING | src.config_validator:_log_validation_summary:254 |   - Text embedding module disabled by configuration
2025-06-18 14:54:59 | INFO | src.pipeline:__init__:124 | Pipeline initialized
2025-06-18 14:54:59 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 14:54:59 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 14:54:59 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 14:54:59 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 14:54:59 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 14:54:59 | WARNING | __main__:main:188 |   - Text embedding module disabled by configuration
2025-06-18 14:54:59 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 14:54:59 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 14:54:59 | INFO | src.database:execute_script:437 | SQL script executed successfully.
2025-06-18 14:54:59 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 14:54:59 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 14:54:59 | INFO | src.pipeline:run:132 | Starting ETL pipeline
2025-06-18 14:54:59 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 14:54:59 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 14:54:59 | INFO | src.database:get_last_processed_id:126 | Last processed ID for niche_text_etl: 0
2025-06-18 14:55:01 | INFO | src.database:fetch_batch:343 | Fetched batch of 1000 records (IDs: 10023001000000046127415 - 10023001000000046336135)
2025-06-18 14:55:01 | INFO | src.pipeline:run:168 | Processing batch 1 (5 records, total: 5/5)
2025-06-18 14:55:01 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 666 characters extracted
2025-06-18 14:55:01 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 991 characters extracted
2025-06-18 14:55:01 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1098 characters extracted
2025-06-18 14:55:01 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 1592 characters extracted
2025-06-18 14:55:01 | INFO | src.parsers.factory:parse_content:87 | Successfully parsed markup content: 785 characters extracted
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKEAST for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'OAKEAST'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and obse), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MILTON for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'MILTON'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MILTON for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'MILTON'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know ), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': "Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value TRAFALGAR for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'TRAFALGAR'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \- motor vehicle. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in fro), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value statement from indepenant witness James Neil for column Remarks could not be coerced to int (error: invalid literal for int() with base 10: 'statement from indepenant witness James Neil'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKWEST for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'OAKWEST'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value 䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯⵴慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙਍值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰⁭挰⁭〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭⁳⁗敎汩匼䅐⁎਍瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎਍瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※਍⼼偓乁䄾牰汩ㄠ‬㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档⁥牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲⁨慂ⱹ传㱮䘯乏㹔⼼㹐਍值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰⁭挰⁭〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎਍瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※਍⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾⁳਍敲畱獥整Ɽ琠楨⁳獩愠搠獥牣灩楴湯漠⁦湡椠据摩湥⁴⁉楷湴獥敳⹤⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾⁮桔牵摳祡‬਍畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠⁮桴⁥楳敤慷歬椠⁮牦湯⁴景ㄠ〰䈠潲瑮⁥਍摒Ⱞ传歡楶汬⹥䤠栠慥摲愠挠牡栠牯⁮潳湵⁤瑳潲杮祬‬湡⁤潬歯摥琠睯牡獤琠敨猠畯摮‮਍⁉慳⁷⁡慭⁮瑳湡楤杮愠牣獯⁳桴⁥瑳敲瑥‬敢睴敥⁮⁡慰歲摥挠牡愠摮琠敨洠癯湩⁧਍牴晡楦⹣吠敨挠牡琠慨⁴慨⁤汢睯⁮桴⁥潨湲眠獡搠楲楶杮瀠獡⁴楨⹭䄠⁳桴⁥慣⁲慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠⁦桴⁥慣⁲楨瑴湩⁧桴⁥数敤瑳楲湡‬湡⁤⁉慳⁷਍桴⁥数敤瑳楲湡戠楥杮瀠獵敨⁤睡祡映潲⁭桴⁥潭楶杮挠牡‮祍椠灭敲獳潩⁮慷⁳桴瑡琠敨ഠ挊牡栠摡戠慲敫⁤獡琠敨栠牯⁮汢睥‬桴湥愠捣汥牥瑡摥瀠獡⁴桴⁥数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯⁮桴⁥瑳敲瑥㰮䘯乏㹔⼼㹐਍值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰⁭挰⁭〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔⁥慣⁲慷⁳਍⁡楬桧⵴潣潬牵摥䌠摡汩慬⁣ⴴ潤牯猠摥湡‮⁉牷瑯⁥潤湷琠敨氠捩湥散渠浵敢⁲ₖ਍䕂䍘ㄴ⸲䤠搠摩鉮⁴敳⁥桴⁥牤癩牥‬獡䤠眠獡氠潯楫杮愠⁴桴⁥数敤瑳楲湡‬桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢⹲⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔਍潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭⁬瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱‬਍〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※਍⼼偓乁㰾䘯乏㹔⼼㹐਍值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰⁭挰⁭〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫⁤敭椠⁦⁉慳⁷桴⁥慣⁲楨⁴桴⁥数敤瑳楲湡‬牯搠摩琠敨ഠ瀊摥獥牴慩⁮楨⁴桴⁥慣⁲楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨⹭吠楨⁳敳潣摮猠散慮楲⁯獩ഠ愊瀠獯楳楢楬祴‮⁉楤湤璒猠敥椠⁴敷汬攠潮杵⁨潴戠⁥畳敲漠⁦楥桴牥漠⁦桴獥⁥਍潰獳扩汩瑩敩⹳⼼但呎㰾倯匾牯祲琠⁯敢猠⁯潬杮爠灥祬湩⹧䤠猠汥潤⁭档捥⁫祭攠洭楡ⱬഠ甊汮獥⁳❉⁭硥数瑣湩⁧潳敭桴湩⹧䈼㹒⼼佂奄㰾䠯䵔㹌 for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '䠼䵔㹌䠼䅅㹄吼呉䕌唾瑮瑩敬㱤启呉䕌㰾呓䱙㹅佂奄笠映湯\u2d74慦業祬›䄠楲污※潦瑮猭穩㩥ㄠ瀰⁴ൽ㰊匯奔䕌㰾䠯䅅㹄䈼䑏㹙\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰牆浯㰺偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰㰠匯䅐㹎慊敭\u2073⁗敎汩匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳※⼼偓乁䈾物桴䐠瑡㩥匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁䄾牰汩ㄠ\u202c㤱㘴⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎㠷䰠扡敲档\u2065牄Ⱞ⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎潎瑲\u2068慂ⱹ传㱮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰匼䅐⁎\u0a0d瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁倾䄱㌠㕒⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰㰾偓乁ഠ猊祴敬∽獭ⵯ慴ⵢ潣湵㩴ㄠ㸢渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰渦獢㭰ഠ㰊匯䅐㹎〷ⴵ㜴ⴶ㘱〳⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䄾\u2073\u0a0d敲畱獥整Ɽ琠楨\u2073獩愠搠獥牣灩楴湯漠\u2066湡椠据摩湥⁴⁉楷湴獥敳\u2e64⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰伾\u206e桔牵摳祡\u202c\u0a0d畊祬ㄠⰶ㈠〰ⰹ愠⁴扡畯⁴㨱㔱倠ⱍ䤠眠獡漠\u206e桴\u2065楳敤慷歬椠\u206e牦湯⁴景ㄠ〰䈠潲瑮\u2065\u0a0d摒Ⱞ传歡楶汬\u2e65䤠栠慥摲愠挠牡栠牯\u206e潳湵\u2064瑳潲杮祬\u202c湡\u2064潬歯摥琠睯牡獤琠敨猠畯摮\u202e\u0a0d⁉慳⁷\u2061慭\u206e瑳湡楤杮愠牣獯\u2073桴\u2065瑳敲瑥\u202c敢睴敥\u206e\u2061慰歲摥挠牡愠摮琠敨洠癯湩\u2067\u0a0d牴晡楦\u2e63吠敨挠牡琠慨⁴慨\u2064汢睯\u206e桴\u2065潨湲眠獡搠楲楶杮瀠獡⁴楨\u2e6d䄠\u2073桴\u2065慣\u2072慰獳摥ഠ琊敨瀠摥獥牴慩Ɱ䤠栠慥摲琠敨猠畯摮漠\u2066桴\u2065慣\u2072楨瑴湩\u2067桴\u2065数敤瑳楲湡\u202c湡\u2064⁉慳⁷\u0a0d桴\u2065数敤瑳楲湡戠楥杮瀠獵敨\u2064睡祡映潲\u206d桴\u2065潭楶杮挠牡\u202e祍椠灭敲獳潩\u206e慷\u2073桴瑡琠敨ഠ挊牡栠摡戠慲敫\u2064獡琠敨栠牯\u206e汢睥\u202c桴湥愠捣汥牥瑡摥瀠獡⁴桴\u2065数敤瑳楲湡愠摮ഠ挊湯楴畮摥搠睯\u206e桴\u2065瑳敲瑥㰮䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰桔\u2065慣\u2072慷\u2073\u0a0d\u2061楬桧\u2d74潣潬牵摥䌠摡汩慬\u2063ⴴ潤牯猠摥湡\u202e⁉牷瑯\u2065潤湷琠敨氠捩湥散渠浵敢\u2072ₖ\u0a0d䕂䍘ㄴ⸲䤠搠摩鉮⁴敳\u2065桴\u2065牤癩牥\u202c獡䤠眠獡氠潯楫杮愠⁴桴\u2065数敤瑳楲湡\u202c桴湥ഠ氊潯楫杮映牯琠敨氠捩湥散瀠慬整渠浵敢\u2e72⼼但呎㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾㩯㹰䘼乏⁔\u0a0d潣潬㵲〣〰〰㸰⼼但呎㰾伯债㰾倯ാ㰊⁐汣獡㵳獍乯牯慭\u206c瑳汹㵥䴢剁䥇㩎〠浣〠浣ㄠ瀰≴㰾但呎挠汯牯⌽〰〰〰䨾汵⁹㠱\u202c\u0a0d〲㤰匼䅐⁎瑳汹㵥洢潳琭扡挭畯瑮›∱☾扮灳☻扮灳☻扮灳☻扮灳☻扮灳☻扮灳※\u0a0d⼼偓乁㰾䘯乏㹔⼼㹐\u0a0d值挠慬獳䴽潳潎浲污猠祴敬∽䅍䝒义›挰\u206d挰\u206d〱瑰㸢䘼乏⁔潣潬㵲〣〰〰㸰敒愠潢敶ഠ猊慴整敭瑮›潙⁵獡敫\u2064敭椠\u2066⁉慳⁷桴\u2065慣\u2072楨⁴桴\u2065数敤瑳楲湡\u202c牯搠摩琠敨ഠ瀊摥獥牴慩\u206e楨⁴桴\u2065慣\u2072楷桴栠獩映獩⁴獡椠⁴敷瑮戠⁹楨\u2e6d吠楨\u2073敳潣摮猠散慮楲\u206f獩ഠ愊瀠獯楳楢楬祴\u202e⁉楤湤璒猠敥椠⁴敷汬攠潮杵\u2068潴戠\u2065畳敲漠\u2066楥桴牥漠\u2066桴獥\u2065\u0a0d潰獳扩汩瑩敩\u2e73⼼但呎㰾倯匾牯祲琠\u206f敢猠\u206f潬杮爠灥祬湩\u2e67䤠猠汥潤\u206d档捥\u206b祭攠洭楡ⱬഠ甊汮獥\u2073❉\u206d硥数瑣湩\u2067潳敭桴湩\u2e67䈼㹒⼼佂奄㰾䠯䵔㹌', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value TRAFALGAR for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'TRAFALGAR'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out du), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:01 | WARNING | src.database:insert_batch:410 | Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}
2025-06-18 14:55:02 | ERROR | src.database:get_connection:92 | Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)') (server=HQPRCAD94BI03, user=PAAdmin, trusted=False)
2025-06-18 14:55:02 | ERROR | src.database:insert_batch:422 | Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')
2025-06-18 14:55:02 | INFO | src.pipeline:_log_progress:360 | Progress: 5 processed, 5 successful, 0 failed, 100.0% success rate, 0.0 records/sec
2025-06-18 14:55:02 | INFO | src.pipeline:run:194 | Reached debug limit of 5 records. Processing complete.
2025-06-18 14:55:02 | INFO | src.pipeline:run:199 | Pipeline completed: {'records_processed': 5, 'records_successful': 5, 'records_failed': 0, 'success_rate': 100.0, 'bytes_processed': 3378, 'text_extracted': 5132, 'duration_seconds': 2.413954, 'records_per_second': 2.071290505121473, 'error_count': 0, 'recent_errors': []}
2025-06-18 14:55:02 | INFO | __main__:main:203 | Pipeline execution completed successfully
2025-06-18 14:55:02 | INFO | __main__:main:204 | Final statistics: {
  "records_processed": 5,
  "records_successful": 5,
  "records_failed": 0,
  "success_rate": 100.0,
  "bytes_processed": 3378,
  "text_extracted": 5132,
  "duration_seconds": 2.413954,
  "records_per_second": 2.071290505121473,
  "error_count": 0,
  "recent_errors": []
}
2025-06-18 14:55:02 | INFO | __main__:run_monitoring_if_enabled:115 | Running daily monitoring tasks...
2025-06-18 14:55:02 | INFO | src.monitoring:run_daily_monitoring:461 | Starting daily monitoring tasks
2025-06-18 14:55:02 | INFO | src.monitoring:run_daily_monitoring:472 | Analyzing logs for the past 24 hours
2025-06-18 14:55:02 | INFO | src.monitoring:run_daily_monitoring:476 | Checking if alert should be sent
2025-06-18 14:55:02 | INFO | src.monitoring:send_alert:226 | Email alerts disabled - skipping alert
2025-06-18 14:55:02 | INFO | src.monitoring:run_daily_monitoring:480 | Rotating old log files
2025-06-18 14:55:02 | INFO | src.monitoring:rotate_logs:429 | Log rotation complete: 0 deleted, 1 kept, 0 bytes freed
2025-06-18 14:55:02 | INFO | src.monitoring:run_daily_monitoring:483 | Daily monitoring tasks completed successfully
2025-06-18 14:55:02 | INFO | __main__:run_monitoring_if_enabled:119 | Monitoring completed: {
  "timestamp": "2025-06-18 14:55:02.067336",
  "log_analysis": {
    "period_start": "2025-06-17 14:55:02.067336",
    "period_end": "2025-06-18 14:55:02.067336",
    "errors": [
      {
        "file": "logs\\niche_etl.log",
        "line": 42,
        "timestamp": "2025-06-18 14:51:51",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)') (server=HQPRCAD94BI03, user=PAAdmin, trusted=False)",
        "context": "src.database:get_connection:92"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 43,
        "timestamp": "2025-06-18 14:51:51",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:416"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 251,
        "timestamp": "2025-06-18 14:55:02",
        "message": "Database connection error: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)') (server=HQPRCAD94BI03, user=PAAdmin, trusted=False)",
        "context": "src.database:get_connection:92"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 252,
        "timestamp": "2025-06-18 14:55:02",
        "message": "Error inserting batch into NicheBlobETLStaging: ('22003', '[22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Arithmetic overflow error converting expression to data type bigint. (8115) (SQLExecDirectW); [22003] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated. (3621)')",
        "context": "src.database:insert_batch:422"
      }
    ],
    "warnings": [
      {
        "file": "logs\\niche_etl.log",
        "line": 5,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 7,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warning: Text embedding module disabled by configuration",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 16,
        "timestamp": "2025-06-18 14:51:48",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 17,
        "timestamp": "2025-06-18 14:51:48",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 18,
        "timestamp": "2025-06-18 14:51:48",
        "message": "- Text embedding module disabled by configuration",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 23,
        "timestamp": "2025-06-18 14:51:49",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 24,
        "timestamp": "2025-06-18 14:51:49",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 25,
        "timestamp": "2025-06-18 14:51:49",
        "message": "- Text embedding module disabled by configuration",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 183,
        "timestamp": "2025-06-18 14:54:59",
        "message": "Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 185,
        "timestamp": "2025-06-18 14:54:59",
        "message": "Configuration warning: Text embedding module disabled by configuration",
        "context": "src.config_validator:add_warning:22"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 194,
        "timestamp": "2025-06-18 14:54:59",
        "message": "Configuration warnings found:",
        "context": "src.config_validator:_log_validation_summary:252"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 195,
        "timestamp": "2025-06-18 14:54:59",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 196,
        "timestamp": "2025-06-18 14:54:59",
        "message": "- Text embedding module disabled by configuration",
        "context": "src.config_validator:_log_validation_summary:254"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 201,
        "timestamp": "2025-06-18 14:54:59",
        "message": "Configuration warnings:",
        "context": "__main__:main:186"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 202,
        "timestamp": "2025-06-18 14:54:59",
        "message": "- Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 203,
        "timestamp": "2025-06-18 14:54:59",
        "message": "- Text embedding module disabled by configuration",
        "context": "__main__:main:188"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 220,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKEAST for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'OAKEAST'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 221,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 222,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 223,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 224,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and obse), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 225,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127433'), 'Niche_Report_ID': Decimal('91423001000000046127420'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 23, 28, 490000), 'Report_Time': datetime.datetime(2009, 8, 17, 1, 0), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000000017250'), 'Niche_Enter_ID': Decimal('150000111000000000013771'), 'Niche_Occurrence_ID': Decimal('90300111000000045644526'), 'Occurrence_Number': '200900121619', 'Occurrence_Type': '933', 'Zone': '208', 'Team': 'OAKEAST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is a security officer and observed six males in above vehicle smoking what he thought were drugs. Writer in area when flagged down by complainant. Writer conducted a traffic stop and observed windows steamy and strong odour of marijuana emitting from inside vehicle. In plain view, writer observed green leafy substance remnants believed to be marijuana on driver's seat, centre console and on driver's lap and rolling papers. All occupants arrested for possession, directed out of vehicle and searched by PC Buceta. Approximately 1 gram of marijuana located on accused. Rights To Counsel, declined lawyer. Accused warned and released. Parents contacted.\", 'text_length': 666, 'original_size': 520, 'decompressed_size': 863, 'compression_ratio': 0.6025492468134415, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 226,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MILTON for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'MILTON'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 227,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MILTON for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'MILTON'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 228,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 229,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 230,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know ), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 231,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128477'), 'Niche_Report_ID': Decimal('91423001000000046128474'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 53, 26, 80000), 'Report_Time': datetime.datetime(2009, 8, 9, 14, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000005032754'), 'Niche_Enter_ID': Decimal('150000111000000038587418'), 'Niche_Occurrence_ID': Decimal('90300111000000045406093'), 'Occurrence_Number': '200900117309', 'Occurrence_Type': '905', 'Zone': '112', 'Team': 'MILTON', 'Municipality': 'MILTON', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': \"Complainant is an employee at the above Petro Canada Gas Station. He advised at the above time a male entered the store and advised that he was there earlier and lost a necklace. Male wanted to know if complainant had located it. Complainant advised he hadn't. Male again stated that he lost it in this store and wanted complainant to return his necklace. Complainant looked again for the necklace but couldn't locate it. Complainant advised the male that he had been working since 23:00 hours last night and found no necklace. Complainant advised male became belligerent and rude, calling him names and yelling at him. Complainant stated the male started throwing chocolate bars at him from the other side of the counter and asking complainant to go outside to fight. Complainant advised he almost went outside but then called police instead. Complainant advised when he called police male left the store in the below vehicle. Complainant believed there was another occupant in the vehicle.\", 'text_length': 991, 'original_size': 585, 'decompressed_size': 1207, 'compression_ratio': 0.48467274233637114, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 232,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value TRAFALGAR for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'TRAFALGAR'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 233,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 234,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 235,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 236,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\- motor vehicle. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in fro), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 237,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046127415'), 'Niche_Report_ID': Decimal('91423001000000046127401'), 'Entered_Time': datetime.datetime(2009, 8, 17, 6, 18, 5, 507000), 'Report_Time': datetime.datetime(2009, 8, 17, 2, 50), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000036979903'), 'Niche_Enter_ID': Decimal('150000111000000000015731'), 'Niche_Occurrence_ID': Decimal('90300111000000045647087'), 'Occurrence_Number': '200900121664', 'Occurrence_Type': '991', 'Zone': '204', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'On 17Aug09, PC Scott was on general patrol on Dundas St East in the Town of Oakville. While driving eastbound on Dundas St, PC Scott observed a white Honda Civic hatchback travelling eastbound in front of him. PC Scott observed this motor vehicle had a temporary validation sticker on the rear licence plate. Investigation revealed the temporary validation had expired. PC Scott initiated a traffic stop. Further investigation revealed the driver of the motor vehicle was suspended, suspension served 16Aug09 at 0900 hours by PC Craig #2392 Peel Regional Police - suspension number 09SUSP-9137211. The driver was arrested for suspend drive. The motor vehicle had no insurance on it and no horn. Driver was released and served Part III Summons for owner operate motor vehicle no insurance, drive while under suspension, no currently validated permit and no horn. The vehicle was seized and towed to a secure lot by CH&R Towing. The driver was also charged with HTA sec 7(1)(a) - offence: driver motor vehicle, no currently validated permit, and HTA sec 75(5) sec - offence: no horn \\\\- motor vehicle.', 'text_length': 1098, 'original_size': 704, 'decompressed_size': 1442, 'compression_ratio': 0.4882108183079057, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 238,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value statement from indepenant witness James Neil for column Remarks could not be coerced to int (error: invalid literal for int() with base 10: 'statement from indepenant witness James Neil'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 239,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKWEST for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'OAKWEST'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 240,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 241,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 242,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 243,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value \u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\u206d\u6330\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\u206d\u6330\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\u2073\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\u206e\u6854\u7275\u6473\u7961\u202c\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\u206e\u6874\u2065\u6973\u6564\u6177\u6b6c\u6920\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\u2065\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\u206e\u6f73\u6e75\u2064\u7473\u6f72\u676e\u796c\u202c\u6e61\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\u202e\u0a0d\u2049\u6173\u2077\u2061\u616d\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\u2073\u6874\u2065\u7473\u6572\u7465\u202c\u6562\u7774\u6565\u206e\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\u2067\u0a0d\u7274\u6661\u6966\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\u2064\u6c62\u776f\u206e\u6874\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\u2e6d\u4120\u2073\u6874\u2065\u6163\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\u2066\u6874\u2065\u6163\u2072\u6968\u7474\u6e69\u2067\u6874\u2065\u6570\u6564\u7473\u6972\u6e61\u202c\u6e61\u2064\u2049\u6173\u2077\u0a0d\u6874\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\u2064\u7761\u7961\u6620\u6f72\u206d\u6874\u2065\u6f6d\u6976\u676e\u6320\u7261\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\u206e\u6177\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\u2064\u7361\u7420\u6568\u6820\u726f\u206e\u6c62\u7765\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\u206e\u6874\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\u206d\u6330\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\u2065\u6163\u2072\u6177\u2073\u0a0d\u2061\u696c\u6867\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\u202e\u2049\u7277\u746f\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\u2072\u2096\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\u2065\u6874\u2065\u7264\u7669\u7265\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\u2065\u6570\u6564\u7473\u6972\u6e61\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\u202c\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\u206d\u6330\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\u2064\u656d\u6920\u2066\u2049\u6173\u2077\u6874\u2065\u6163\u2072\u6968\u2074\u6874\u2065\u6570\u6564\u7473\u6972\u6e61\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\u206e\u6968\u2074\u6874\u2065\u6163\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\u2e6d\u5420\u6968\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\u2068\u6f74\u6220\u2065\u7573\u6572\u6f20\u2066\u6965\u6874\u7265\u6f20\u2066\u6874\u7365\u2065\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\u206f\u6562\u7320\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\u2e67\u4920\u7320\u6c65\u6f64\u206d\u6863\u6365\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\u2073\u2749\u206d\u7865\u6570\u7463\u6e69\u2067\u6f73\u656d\u6874\u6e69\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 244,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128594'), 'Niche_Report_ID': Decimal('91623001000000046128591'), 'Entered_Time': datetime.datetime(2009, 8, 17, 7, 51, 37, 917000), 'Report_Time': datetime.datetime(2009, 8, 17, 7, 51), 'Remarks': 'statement from indepenant witness James Neil', 'Niche_Author_ID': Decimal('150000111000000000013582'), 'Niche_Enter_ID': Decimal('150000111000000000013582'), 'Niche_Occurrence_ID': Decimal('90300111000000044599642'), 'Occurrence_Number': '200900104134', 'Occurrence_Type': '953', 'Zone': '213', 'Team': 'OAKWEST', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': '\u483c\u4d54\u3e4c\u483c\u4145\u3e44\u543c\u5449\u454c\u553e\u746e\u7469\u656c\u3c64\u542f\u5449\u454c\u3c3e\u5453\u4c59\u3e45\u4f42\u5944\u7b20\u6620\u6e6f\\u2d74\u6166\u696d\u796c\u203a\u4120\u6972\u6c61\u203b\u6f66\u746e\u732d\u7a69\u3a65\u3120\u7030\u2074\u0d7d\u3c0a\u532f\u5954\u454c\u3c3e\u482f\u4145\u3e44\u423c\u444f\u3e59\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u7246\u6d6f\u3c3a\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u3c20\u532f\u4150\u3e4e\u614a\u656d\\u2073\u2057\u654e\u6c69\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\u2f3c\u5053\u4e41\u423e\u7269\u6874\u4420\u7461\u3a65\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u413e\u7270\u6c69\u3120\\u202c\u3931\u3634\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3837\u4c20\u6261\u6572\u6863\\u2065\u7244\u2c2e\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u6f4e\u7472\\u2068\u6142\u2c79\u4f20\u3c6e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u533c\u4150\u204e\\u0a0d\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u503e\u4131\u3320\u3552\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u3c3e\u5053\u4e41\u0d20\u730a\u7974\u656c\u223d\u736d\u2d6f\u6174\u2d62\u6f63\u6e75\u3a74\u3120\u3e22\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u6e26\u7362\u3b70\u0d20\u3c0a\u532f\u4150\u3e4e\u3037\u2d35\u3734\u2d36\u3631\u3033\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u413e\\u2073\\u0a0d\u6572\u7571\u7365\u6574\u2c64\u7420\u6968\\u2073\u7369\u6120\u6420\u7365\u7263\u7069\u6974\u6e6f\u6f20\\u2066\u6e61\u6920\u636e\u6469\u6e65\u2074\u2049\u6977\u6e74\u7365\u6573\\u2e64\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4f3e\\u206e\u6854\u7275\u6473\u7961\\u202c\\u0a0d\u754a\u796c\u3120\u2c36\u3220\u3030\u2c39\u6120\u2074\u6261\u756f\u2074\u3a31\u3531\u5020\u2c4d\u4920\u7720\u7361\u6f20\\u206e\u6874\\u2065\u6973\u6564\u6177\u6b6c\u6920\\u206e\u7266\u6e6f\u2074\u666f\u3120\u3030\u4220\u6f72\u746e\\u2065\\u0a0d\u6452\u2c2e\u4f20\u6b61\u6976\u6c6c\\u2e65\u4920\u6820\u6165\u6472\u6120\u6320\u7261\u6820\u726f\\u206e\u6f73\u6e75\\u2064\u7473\u6f72\u676e\u796c\\u202c\u6e61\\u2064\u6f6c\u6b6f\u6465\u7420\u776f\u7261\u7364\u7420\u6568\u7320\u756f\u646e\\u202e\\u0a0d\u2049\u6173\u2077\\u2061\u616d\\u206e\u7473\u6e61\u6964\u676e\u6120\u7263\u736f\\u2073\u6874\\u2065\u7473\u6572\u7465\\u202c\u6562\u7774\u6565\\u206e\\u2061\u6170\u6b72\u6465\u6320\u7261\u6120\u646e\u7420\u6568\u6d20\u766f\u6e69\\u2067\\u0a0d\u7274\u6661\u6966\\u2e63\u5420\u6568\u6320\u7261\u7420\u6168\u2074\u6168\\u2064\u6c62\u776f\\u206e\u6874\\u2065\u6f68\u6e72\u7720\u7361\u6420\u6972\u6976\u676e\u7020\u7361\u2074\u6968\\u2e6d\u4120\\u2073\u6874\\u2065\u6163\\u2072\u6170\u7373\u6465\u0d20\u740a\u6568\u7020\u6465\u7365\u7274\u6169\u2c6e\u4920\u6820\u6165\u6472\u7420\u6568\u7320\u756f\u646e\u6f20\\u2066\u6874\\u2065\u6163\\u2072\u6968\u7474\u6e69\\u2067\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6e61\\u2064\u2049\u6173\u2077\\u0a0d\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6220\u6965\u676e\u7020\u7375\u6568\\u2064\u7761\u7961\u6620\u6f72\\u206d\u6874\\u2065\u6f6d\u6976\u676e\u6320\u7261\\u202e\u794d\u6920\u706d\u6572\u7373\u6f69\\u206e\u6177\\u2073\u6874\u7461\u7420\u6568\u0d20\u630a\u7261\u6820\u6461\u6220\u6172\u656b\\u2064\u7361\u7420\u6568\u6820\u726f\\u206e\u6c62\u7765\\u202c\u6874\u6e65\u6120\u6363\u6c65\u7265\u7461\u6465\u7020\u7361\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\u6120\u646e\u0d20\u630a\u6e6f\u6974\u756e\u6465\u6420\u776f\\u206e\u6874\\u2065\u7473\u6572\u7465\u3c2e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6854\\u2065\u6163\\u2072\u6177\\u2073\\u0a0d\\u2061\u696c\u6867\\u2d74\u6f63\u6f6c\u7275\u6465\u4320\u6461\u6c69\u616c\\u2063\u2d34\u6f64\u726f\u7320\u6465\u6e61\\u202e\u2049\u7277\u746f\\u2065\u6f64\u6e77\u7420\u6568\u6c20\u6369\u6e65\u6563\u6e20\u6d75\u6562\\u2072\u2096\\u0a0d\u4542\u4358\u3134\u2e32\u4920\u6420\u6469\u926e\u2074\u6573\\u2065\u6874\\u2065\u7264\u7669\u7265\\u202c\u7361\u4920\u7720\u7361\u6c20\u6f6f\u696b\u676e\u6120\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u6874\u6e65\u0d20\u6c0a\u6f6f\u696b\u676e\u6620\u726f\u7420\u6568\u6c20\u6369\u6e65\u6563\u7020\u616c\u6574\u6e20\u6d75\u6562\\u2e72\u2f3c\u4f46\u544e\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u3a6f\u3e70\u463c\u4e4f\u2054\\u0a0d\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u2f3c\u4f46\u544e\u3c3e\u4f2f\u503a\u3c3e\u502f\u0d3e\u3c0a\u2050\u6c63\u7361\u3d73\u734d\u4e6f\u726f\u616d\\u206c\u7473\u6c79\u3d65\u4d22\u5241\u4947\u3a4e\u3020\u6d63\u3020\u6d63\u3120\u7030\u2274\u3c3e\u4f46\u544e\u6320\u6c6f\u726f\u233d\u3030\u3030\u3030\u4a3e\u6c75\u2079\u3831\\u202c\\u0a0d\u3032\u3930\u533c\u4150\u204e\u7473\u6c79\u3d65\u6d22\u6f73\u742d\u6261\u632d\u756f\u746e\u203a\u2231\u263e\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u263b\u626e\u7073\u203b\\u0a0d\u2f3c\u5053\u4e41\u3c3e\u462f\u4e4f\u3e54\u2f3c\u3e50\\u0a0d\u503c\u6320\u616c\u7373\u4d3d\u6f73\u6f4e\u6d72\u6c61\u7320\u7974\u656c\u223d\u414d\u4752\u4e49\u203a\u6330\\u206d\u6330\\u206d\u3031\u7470\u3e22\u463c\u4e4f\u2054\u6f63\u6f6c\u3d72\u3023\u3030\u3030\u3e30\u6552\u6120\u6f62\u6576\u0d20\u730a\u6174\u6574\u656d\u746e\u203a\u6f59\u2075\u7361\u656b\\u2064\u656d\u6920\\u2066\u2049\u6173\u2077\u6874\\u2065\u6163\\u2072\u6968\u2074\u6874\\u2065\u6570\u6564\u7473\u6972\u6e61\\u202c\u726f\u6420\u6469\u7420\u6568\u0d20\u700a\u6465\u7365\u7274\u6169\\u206e\u6968\u2074\u6874\\u2065\u6163\\u2072\u6977\u6874\u6820\u7369\u6620\u7369\u2074\u7361\u6920\u2074\u6577\u746e\u6220\u2079\u6968\\u2e6d\u5420\u6968\\u2073\u6573\u6f63\u646e\u7320\u6563\u616e\u6972\\u206f\u7369\u0d20\u610a\u7020\u736f\u6973\u6962\u696c\u7974\\u202e\u2049\u6964\u6e64\u7492\u7320\u6565\u6920\u2074\u6577\u6c6c\u6520\u6f6e\u6775\\u2068\u6f74\u6220\\u2065\u7573\u6572\u6f20\\u2066\u6965\u6874\u7265\u6f20\\u2066\u6874\u7365\\u2065\\u0a0d\u6f70\u7373\u6269\u6c69\u7469\u6569\\u2e73\u2f3c\u4f46\u544e\u3c3e\u502f\u533e\u726f\u7972\u7420\\u206f\u6562\u7320\\u206f\u6f6c\u676e\u7220\u7065\u796c\u6e69\\u2e67\u4920\u7320\u6c65\u6f64\\u206d\u6863\u6365\\u206b\u796d\u6520\u6d2d\u6961\u2c6c\u0d20\u750a\u6c6e\u7365\\u2073\u2749\\u206d\u7865\u6570\u7463\u6e69\\u2067\u6f73\u656d\u6874\u6e69\\u2e67\u423c\u3e52\u2f3c\u4f42\u5944\u3c3e\u482f\u4d54\u3e4c', 'text_length': 1592, 'original_size': 1017, 'decompressed_size': 3184, 'compression_ratio': 0.31940954773869346, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 895795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 245,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value TRAFALGAR for column Team could not be coerced to int (error: invalid literal for int() with base 10: 'TRAFALGAR'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 246,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value OAKVILLE for column Municipality could not be coerced to int (error: invalid literal for int() with base 10: 'OAKVILLE'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 247,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value HTM for column real_type could not be coerced to int (error: invalid literal for int() with base 10: 'HTM'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 248,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value markup for column category could not be coerced to int (error: invalid literal for int() with base 10: 'markup'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 249,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up. for column normalized_text could not be coerced to int (error: invalid literal for int() with base 10: 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out du), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      },
      {
        "file": "logs\\niche_etl.log",
        "line": 250,
        "timestamp": "2025-06-18 14:55:01",
        "message": "Value MarkupParser for column parser_used could not be coerced to int (error: invalid literal for int() with base 10: 'MarkupParser'), setting to NULL. Record: {'SourceId': Decimal('10023001000000046128942'), 'Niche_Report_ID': Decimal('91423001000000046128915'), 'Entered_Time': datetime.datetime(2009, 8, 17, 8, 9, 51, 93000), 'Report_Time': datetime.datetime(2009, 8, 9, 18, 30), 'Remarks': None, 'Niche_Author_ID': Decimal('150000111000000017483831'), 'Niche_Enter_ID': Decimal('150000111000000027346418'), 'Niche_Occurrence_ID': Decimal('90300111000000045410348'), 'Occurrence_Number': '200900117413', 'Occurrence_Type': '960', 'Zone': '207', 'Team': 'TRAFALGAR', 'Municipality': 'OAKVILLE', 'real_type': 'HTM', 'category': 'markup', 'normalized_text': 'Complainant and suspect have been together for 7 years and have been married since January 2006. The two lived in Ajax together up until approximately a month and a half ago when suspect moved out due to marital problems. Complainant has since moved to Milton and is in the process of getting separated legally from suspect. On above date, complainant attended 20 Division with 2 emails she had received from the suspect. Complainant is concerned for her safety based on the emails. Writer read the emails and found noting in the content to be of a criminal nature. While speaking with complainant, she made reference to several separate incident to which the suspect assaulted her. Last incident was approximately 3 years ago. Report forwarded to Domestic Violence Unit for follow up.', 'text_length': 785, 'original_size': 552, 'decompressed_size': 929, 'compression_ratio': 0.5941872981700753, 'parser_used': 'MarkupParser', 'ProcessedTime': datetime.datetime(2025, 6, 18, 14, 55, 1, 896795), 'parsing_successful': True, 'text_embedding': None, 'embedding_dimension': None, 'chunk_count': None, 'embedding_type': None}",
        "context": "src.database:insert_batch:410"
      }
    ],
    "total_errors": 4,
    "total_warnings": 47,
    "files_analyzed": [
      "logs\\niche_etl.log"
    ],
    "summary": {
      "has_errors": true,
      "has_warnings": true,
      "error_categories": {
        "Database": 2,
        "Other": 2
      },
      "warning_categories": {
        "Database": 6,
        "Other": 10,
        "Parsing": 31
      },
      "most_common_errors": [],
      "most_common_warnings": []
    }
  },
  "email_sent": true,
  "log_rotation": {
    "files_deleted": [],
    "files_kept": [
      "logs\\niche_etl.log"
    ],
    "total_deleted": 0,
    "total_kept": 1,
    "space_freed": 0
  }
}
2025-06-18 14:55:02 | INFO | __main__:main:215 | Niche Text ETL completed successfully
2025-06-18 15:17:08 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-18 15:17:08 | INFO | __main__:main:160 | Niche Text ETL starting with simplified execution model
2025-06-18 15:17:08 | INFO | __main__:main:163 | Initializing pipeline...
2025-06-18 15:17:08 | INFO | src.config_validator:validate_all:55 | Starting comprehensive configuration validation...
2025-06-18 15:17:08 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 15:17:08 | INFO | src.config_validator:set_module_flag:34 | Module 'email_enabled' is disabled
2025-06-18 15:17:08 | WARNING | src.config_validator:add_warning:22 | Configuration warning: Text embedding module disabled by configuration
2025-06-18 15:17:08 | INFO | src.config_validator:set_module_flag:34 | Module 'embedding_enabled' is disabled
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:240 | Configuration validation completed
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:241 | Overall validation status: PASSED
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:242 | Warnings: 2
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:243 | Errors: 0
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:246 | Module status:
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:249 |   - email_enabled: DISABLED
2025-06-18 15:17:08 | INFO | src.config_validator:_log_validation_summary:249 |   - embedding_enabled: DISABLED
2025-06-18 15:17:08 | WARNING | src.config_validator:_log_validation_summary:252 | Configuration warnings found:
2025-06-18 15:17:08 | WARNING | src.config_validator:_log_validation_summary:254 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 15:17:08 | WARNING | src.config_validator:_log_validation_summary:254 |   - Text embedding module disabled by configuration
2025-06-18 15:17:08 | INFO | src.pipeline:__init__:124 | Pipeline initialized
2025-06-18 15:17:08 | INFO | __main__:main:169 | Validating configuration...
2025-06-18 15:17:08 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 15:17:09 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 15:17:09 | WARNING | __main__:main:186 | Configuration warnings:
2025-06-18 15:17:09 | WARNING | __main__:main:188 |   - Email module disabled - SMTP connection failed: SMTP connection test failed: [Errno 11001] getaddrinfo failed
2025-06-18 15:17:09 | WARNING | __main__:main:188 |   - Text embedding module disabled by configuration
2025-06-18 15:17:09 | INFO | __main__:main:190 | Configuration validation completed
2025-06-18 15:17:09 | INFO | __main__:setup_database_if_needed:70 | Auto-setting up database...
2025-06-18 15:17:09 | INFO | src.database:execute_script:495 | SQL script executed successfully.
2025-06-18 15:17:09 | INFO | __main__:setup_database_if_needed:98 | Database setup completed successfully
2025-06-18 15:17:09 | INFO | __main__:main:199 | Starting ETL pipeline execution...
2025-06-18 15:17:09 | INFO | src.pipeline:run:132 | Starting ETL pipeline
2025-06-18 15:17:09 | INFO | src.pipeline:_test_connections:210 | Testing database connections...
2025-06-18 15:17:09 | INFO | src.pipeline:_test_connections:218 | Database connections successful
2025-06-18 15:17:09 | INFO | src.database:get_last_processed_id:130 | Last processed ID for niche_text_etl: 0
2025-06-18 15:17:09 | ERROR | src.database:get_connection:92 | Database connection error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'TRANSACTION_DTS'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'OPERATION_SEQUENCE'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)") (server=HQDCPRUARMSDBRP, user=PARead, trusted=False)
2025-06-18 15:17:09 | ERROR | src.database:fetch_batch:368 | Error fetching batch: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'TRANSACTION_DTS'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'OPERATION_SEQUENCE'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 15:17:09 | ERROR | src.pipeline:run:205 | Pipeline failed: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'TRANSACTION_DTS'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'OPERATION_SEQUENCE'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-18 15:17:09 | ERROR | __main__:main:238 | Unexpected error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'TRANSACTION_DTS'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'OPERATION_SEQUENCE'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
