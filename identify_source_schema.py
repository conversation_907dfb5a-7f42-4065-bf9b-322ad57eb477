#!/usr/bin/env python3
"""
Script to identify the actual source table schema.
This will help us understand what columns actually exist in TBL_BlobData.
"""

import sys
import os
import logging

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config import load_config
from database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
)
logger = logging.getLogger(__name__)

def identify_source_schema():
    """Identify the actual schema of the source table."""
    try:
        config = load_config()
        
        # Connect to source database
        source_db = DatabaseManager(config.database['source'])
        
        with source_db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Get table schema information
            schema_query = f"""
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'TBL_BlobData'
            AND TABLE_SCHEMA = '{config.database['source'].db_schema}'
            ORDER BY ORDINAL_POSITION
            """
            
            logger.info("Querying source table schema...")
            cursor.execute(schema_query)
            columns = cursor.fetchall()
            
            if not columns:
                logger.error("No columns found! Check table name and schema.")
                return None
            
            logger.info(f"Found {len(columns)} columns in TBL_BlobData:")
            logger.info("-" * 80)
            logger.info(f"{'Column Name':<25} {'Data Type':<15} {'Nullable':<10} {'Max Length':<12} {'Precision':<10} {'Scale':<8}")
            logger.info("-" * 80)
            
            actual_columns = []
            for col in columns:
                col_name = col[0]
                data_type = col[1]
                nullable = col[2]
                max_length = col[3] if col[3] else ''
                precision = col[4] if col[4] else ''
                scale = col[5] if col[5] else ''
                
                logger.info(f"{col_name:<25} {data_type:<15} {nullable:<10} {str(max_length):<12} {str(precision):<10} {str(scale):<8}")
                actual_columns.append({
                    'name': col_name,
                    'type': data_type,
                    'nullable': nullable,
                    'max_length': max_length,
                    'precision': precision,
                    'scale': scale
                })
            
            logger.info("-" * 80)
            
            # Check for specific columns we expected
            expected_columns = [
                'Id', 'ID', 'TRANSACTION_DTS', 'ACCDomain', 'Data', 
                'OPERATION_SEQUENCE', 'HostFieldName', 'HostId', 'Type', 'Size', 'RowVersionNumber'
            ]
            
            actual_column_names = [col['name'] for col in actual_columns]
            
            logger.info("\nColumn availability check:")
            for expected in expected_columns:
                if expected in actual_column_names:
                    logger.info(f"✓ {expected} - EXISTS")
                else:
                    logger.warning(f"✗ {expected} - MISSING")
                    # Check for similar names (case variations)
                    similar = [name for name in actual_column_names if name.lower() == expected.lower()]
                    if similar:
                        logger.info(f"  → Similar: {similar[0]}")
            
            # Sample a few records to understand the data
            logger.info("\nSampling first 3 records...")
            sample_query = f"""
            SELECT TOP 3 *
            FROM [{config.database['source'].db_schema}].[TBL_BlobData]
            ORDER BY Id
            """
            
            try:
                cursor.execute(sample_query)
                sample_rows = cursor.fetchall()
                
                if sample_rows:
                    logger.info(f"Sample data (showing first 3 records):")
                    for i, row in enumerate(sample_rows):
                        logger.info(f"Record {i+1}:")
                        for j, col in enumerate(actual_columns):
                            value = row[j]
                            if isinstance(value, bytes) and len(value) > 50:
                                value_str = f"<binary data: {len(value)} bytes>"
                            else:
                                value_str = str(value)[:100] if value else "NULL"
                            logger.info(f"  {col['name']}: {value_str}")
                        logger.info("")
                        
            except Exception as e:
                logger.warning(f"Could not sample data: {e}")
            
            return actual_columns
            
    except Exception as e:
        logger.error(f"Error identifying source schema: {e}")
        return None

def generate_corrected_sql():
    """Generate corrected SQL query based on actual schema."""
    actual_columns = identify_source_schema()
    
    if not actual_columns:
        return
    
    logger.info("\nGenerating corrected SQL query...")
    
    # Map expected columns to actual columns
    column_mapping = {}
    actual_names = [col['name'] for col in actual_columns]
    
    # Try to map columns (case-insensitive)
    expected_to_actual = {
        'Id': None,
        'ID': None,
        'TRANSACTION_DTS': None,
        'ACCDomain': None,
        'Data': None,
        'OPERATION_SEQUENCE': None,
        'HostFieldName': None,
        'HostId': None,
        'Type': None,
        'Size': None,
        'RowVersionNumber': None
    }
    
    for expected in expected_to_actual.keys():
        for actual in actual_names:
            if actual.lower() == expected.lower():
                expected_to_actual[expected] = actual
                break
    
    # Generate SQL with available columns
    logger.info("\nRecommended SQL query updates:")
    logger.info("=" * 60)
    
    available_columns = []
    for expected, actual in expected_to_actual.items():
        if actual:
            if expected in ['Id', 'HostId']:
                available_columns.append(f"CAST(BD.{actual} AS DECIMAL(18,2)) AS {actual}")
            else:
                available_columns.append(f"BD.{actual}")
    
    if available_columns:
        sql_select = ",\n                ".join(available_columns)
        logger.info("SELECT clause should include:")
        logger.info(sql_select)
    
    logger.info("\nColumns to remove from pipeline (not available in source):")
    for expected, actual in expected_to_actual.items():
        if not actual:
            logger.warning(f"- {expected}")

def main():
    """Main function."""
    logger.info("Starting source table schema identification...")
    
    try:
        generate_corrected_sql()
        
    except Exception as e:
        logger.error(f"Schema identification failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
